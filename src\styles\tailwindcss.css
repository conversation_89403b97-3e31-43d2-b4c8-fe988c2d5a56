@import "tailwindcss";

@theme {
  /* Custom colors */
  --color-primary: #ef4444;
  --color-primary-50: #fef2f2;
  --color-primary-100: #fee2e2;
  --color-primary-200: #fecaca;
  --color-primary-300: #fca5a5;
  --color-primary-400: #f87171;
  --color-primary-500: #ef4444;
  --color-primary-600: #dc2626;
  --color-primary-700: #b91c1c;
  --color-primary-800: #991b1b;
  --color-primary-900: #7f1d1d;

  /* Custom background gradients */
  --color-custom-gradient-from: #667eea;
  --color-custom-gradient-to: #764ba2;
}

/* Custom utility classes */
.bg-custom-gradient {
  background: linear-gradient(135deg, var(--color-custom-gradient-from) 0%, var(--color-custom-gradient-to) 100%);
}

/* Additional custom gradients */
.bg-sunset-gradient {
  background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);
}

.bg-ocean-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-forest-gradient {
  background: linear-gradient(135deg, #134e5e 0%, #71b280 100%);
}

/* Custom shadows */
.shadow-custom {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-primary {
  box-shadow: 0 10px 25px -5px rgba(239, 68, 68, 0.25), 0 10px 10px -5px rgba(239, 68, 68, 0.1);
}

/* Custom animations */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom button styles */
.btn-primary {
  @apply bg-primary-500 hover:bg-primary-600 text-white px-6 py-2 rounded-lg transition-colors shadow-primary;
}

.btn-outline {
  @apply border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white px-6 py-2 rounded-lg transition-colors;
}