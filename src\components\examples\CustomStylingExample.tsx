const CustomStylingExample = () => {
  return (
    <div className="p-8 space-y-6">
      <h1 className="text-3xl font-bold text-primary-600">Custom Styling Examples</h1>
      
      {/* Custom gradient background */}
      <div className="bg-custom-gradient p-6 rounded-lg text-white">
        <h2 className="text-xl font-semibold mb-2">Custom Gradient Background</h2>
        <p>This uses the custom gradient defined in your CSS file.</p>
      </div>
      
      {/* Custom primary colors */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Custom Primary Color Palette</h2>
        <div className="grid grid-cols-5 gap-2">
          <div className="bg-primary-100 p-4 rounded text-center text-primary-900">100</div>
          <div className="bg-primary-300 p-4 rounded text-center text-primary-900">300</div>
          <div className="bg-primary-500 p-4 rounded text-center text-white">500</div>
          <div className="bg-primary-700 p-4 rounded text-center text-white">700</div>
          <div className="bg-primary-900 p-4 rounded text-center text-white">900</div>
        </div>
      </div>
      
      {/* Additional gradient examples */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">More Custom Gradients</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-sunset-gradient p-6 rounded-lg text-white text-center">
            <h3 className="font-semibold">Sunset Gradient</h3>
            <p className="text-sm opacity-90">bg-sunset-gradient</p>
          </div>
          <div className="bg-ocean-gradient p-6 rounded-lg text-white text-center">
            <h3 className="font-semibold">Ocean Gradient</h3>
            <p className="text-sm opacity-90">bg-ocean-gradient</p>
          </div>
          <div className="bg-forest-gradient p-6 rounded-lg text-white text-center">
            <h3 className="font-semibold">Forest Gradient</h3>
            <p className="text-sm opacity-90">bg-forest-gradient</p>
          </div>
        </div>
      </div>

      {/* Custom shadows */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Custom Shadows</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white p-6 rounded-lg shadow-custom">
            <h3 className="font-semibold text-gray-800">Custom Shadow</h3>
            <p className="text-gray-600">Using shadow-custom class</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-primary">
            <h3 className="font-semibold text-gray-800">Primary Shadow</h3>
            <p className="text-gray-600">Using shadow-primary class</p>
          </div>
        </div>
      </div>

      {/* Buttons with custom classes */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Custom Button Classes</h2>
        <div className="space-x-4">
          <button className="btn-primary">
            Primary Button
          </button>
          <button className="btn-outline">
            Outline Button
          </button>
        </div>
      </div>

      {/* Animation example */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-800">Custom Animation</h2>
        <div className="bg-white p-6 rounded-lg shadow-custom animate-fade-in">
          <h3 className="font-semibold text-gray-800">Fade In Animation</h3>
          <p className="text-gray-600">This card uses the animate-fade-in class</p>
        </div>
      </div>
    </div>
  );
};

export default CustomStylingExample;
